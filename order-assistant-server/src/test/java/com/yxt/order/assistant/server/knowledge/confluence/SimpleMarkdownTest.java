package com.yxt.order.assistant.server.knowledge.confluence;

import java.util.ArrayList;
import java.util.List;

/**
 * 简单的Markdown测试类，测试空标题移除逻辑
 */
public class SimpleMarkdownTest {

    /**
     * 移除空标题（没有内容的标题）
     */
    public static String removeEmptyHeadings(String content) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        String[] lines = content.split("\n");
        List<String> resultLines = new ArrayList<>();

        for (int i = 0; i < lines.length; i++) {
            String currentLine = lines[i].trim();

            // 检查是否是标题行
            if (isHeadingLine(currentLine)) {
                // 检查这个标题下是否有真正的内容
                if (hasRealContentUnderHeading(lines, i)) {
                    // 有真正的内容，保留这个标题
                    resultLines.add(lines[i]);
                } else {
                    // 没有真正的内容，移除这个标题
                    System.out.println("🗑️ 移除空标题: " + currentLine);
                }
            } else {
                // 不是标题行，直接保留
                resultLines.add(lines[i]);
            }
        }

        String result = String.join("\n", resultLines);
        System.out.println("📝 标题清理完成，原始行数: " + lines.length + ", 清理后行数: " + resultLines.size());

        return result;
    }

    /**
     * 检查标题下是否有真正的内容
     */
    public static boolean hasRealContentUnderHeading(String[] lines, int headingIndex) {
        if (headingIndex >= lines.length - 1) {
            return false; // 标题是最后一行，没有内容
        }

        String currentHeading = lines[headingIndex].trim();
        int currentLevel = getHeadingLevel(currentHeading);

        // 从标题的下一行开始查找
        for (int i = headingIndex + 1; i < lines.length; i++) {
            String line = lines[i].trim();

            if (line.isEmpty()) {
                continue; // 跳过空行
            }

            if (isHeadingLine(line)) {
                int lineLevel = getHeadingLevel(line);
                if (lineLevel < currentLevel) {
                    // 遇到更高级的标题，说明当前标题的内容区域结束了，没有找到内容
                    return false;
                } else if (lineLevel == currentLevel) {
                    // 遇到同级标题，说明当前标题的内容区域结束了，没有找到内容
                    return false;
                } else {
                    // 遇到下级标题，检查这个下级标题是否有内容
                    if (hasRealContentUnderHeading(lines, i)) {
                        return true; // 子标题有内容，所以当前标题也算有内容
                    }
                    // 继续查找其他子标题
                    continue;
                }
            }

            // 检查是否是真正的内容
            if (!isEmptyContentMarker(line)) {
                return true; // 找到真正的内容
            }
        }

        return false; // 没有找到真正的内容
    }

    /**
     * 获取标题级别
     */
    public static int getHeadingLevel(String line) {
        if (!isHeadingLine(line)) {
            return 0;
        }

        int level = 0;
        for (char c : line.toCharArray()) {
            if (c == '#') {
                level++;
            } else {
                break;
            }
        }
        return level;
    }

    /**
     * 检查是否是标题行
     */
    public static boolean isHeadingLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return false;
        }
        line = line.trim();
        return line.startsWith("#") && line.length() > 1 && line.charAt(1) == ' ';
    }

    /**
     * 查找下一个非空内容行
     */
    public static int findNextContentLine(String[] lines, int startIndex) {
        for (int i = startIndex; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty()) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 检查是否是空内容标记（如只有注释、PS等）
     */
    public static boolean isEmptyContentMarker(String line) {
        if (line == null || line.trim().isEmpty()) {
            return true;
        }
        
        line = line.trim();
        
        // 检查是否只是PS注释
        if (line.startsWith("*PS：") || line.startsWith("*PS:") || 
            line.startsWith("*注：") || line.startsWith("*注:") ||
            line.matches("^\\*.*\\*$")) {
            return true;
        }
        
        // 检查是否只是空的格式化内容
        if (line.matches("^[\\*_\\-\\s]*$")) {
            return true;
        }
        
        return false;
    }

    /**
     * 查找下一个真正的内容行（排除注释和空格式化内容）
     */
    public static int findNextRealContent(String[] lines, int startIndex) {
        for (int i = startIndex; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty() && !isEmptyContentMarker(line)) {
                return i;
            }
        }
        return -1;
    }

    public static void main(String[] args) {
        System.out.println("🧪 开始测试空标题移除功能...\n");

        // 测试用例1：包含空标题的markdown
        String input1 = "# ㊃ 交易生产组\n\n" +
                       "# 一、组织简介\n\n" +
                       "## 1.1 组织架构\n\n" +
                       "*PS：成都产研中心工位布局图 *\n\n" +
                       "## 1.2 组织机制\n\n" +
                       "## 1.3 文化氛围\n\n" +
                       "# 二、使命愿景\n\n" +
                       "使命：发挥技术能力，挖掘数据价值，赋能医药大健康行业。\n\n" +
                       "愿景：建设行业内最具价值的医药大健康产品技术团队。";

        System.out.println("📋 测试用例1 - 原始内容:");
        System.out.println(input1);
        System.out.println("\n🔄 处理中...");
        
        String result1 = removeEmptyHeadings(input1);
        
        System.out.println("\n📄 处理后内容:");
        System.out.println(result1);

        // 验证结果
        boolean test1Pass = true;
        if (result1.contains("## 1.1 组织架构")) {
            System.out.println("❌ 测试失败: 应该移除空标题 '## 1.1 组织架构'");
            test1Pass = false;
        }
        if (result1.contains("## 1.2 组织机制")) {
            System.out.println("❌ 测试失败: 应该移除空标题 '## 1.2 组织机制'");
            test1Pass = false;
        }
        if (result1.contains("## 1.3 文化氛围")) {
            System.out.println("❌ 测试失败: 应该移除空标题 '## 1.3 文化氛围'");
            test1Pass = false;
        }
        if (!result1.contains("# ㊃ 交易生产组")) {
            System.out.println("❌ 测试失败: 应该保留有子标题的标题 '# ㊃ 交易生产组'");
            test1Pass = false;
        }
        if (!result1.contains("# 一、组织简介")) {
            System.out.println("❌ 测试失败: 应该保留有子标题的标题 '# 一、组织简介'");
            test1Pass = false;
        }
        if (!result1.contains("# 二、使命愿景")) {
            System.out.println("❌ 测试失败: 应该保留有内容的标题");
            test1Pass = false;
        }
        if (!result1.contains("使命：发挥技术能力")) {
            System.out.println("❌ 测试失败: 应该保留标题下的内容");
            test1Pass = false;
        }

        if (test1Pass) {
            System.out.println("✅ 测试用例1通过");
        }

        System.out.println("\n==================================================\n");

        // 测试用例2：标题下只有注释的情况
        String input2 = "# 主标题\n\n" +
                       "## 空标题1\n\n" +
                       "*PS：这只是一个注释*\n\n" +
                       "## 空标题2\n\n" +
                       "*注：另一个注释*\n\n" +
                       "## 有内容的标题\n\n" +
                       "这里有真正的内容。\n\n" +
                       "## 另一个空标题\n\n";

        System.out.println("📋 测试用例2 - 原始内容:");
        System.out.println(input2);
        System.out.println("\n🔄 处理中...");
        
        String result2 = removeEmptyHeadings(input2);
        
        System.out.println("\n📄 处理后内容:");
        System.out.println(result2);

        // 验证结果
        boolean test2Pass = true;
        if (result2.contains("## 空标题1")) {
            System.out.println("❌ 测试失败: 应该移除只有注释的标题");
            test2Pass = false;
        }
        if (result2.contains("## 空标题2")) {
            System.out.println("❌ 测试失败: 应该移除只有注释的标题");
            test2Pass = false;
        }
        if (result2.contains("## 另一个空标题")) {
            System.out.println("❌ 测试失败: 应该移除末尾的空标题");
            test2Pass = false;
        }
        if (!result2.contains("## 有内容的标题")) {
            System.out.println("❌ 测试失败: 应该保留有真正内容的标题");
            test2Pass = false;
        }
        if (!result2.contains("这里有真正的内容")) {
            System.out.println("❌ 测试失败: 应该保留真正的内容");
            test2Pass = false;
        }

        if (test2Pass) {
            System.out.println("✅ 测试用例2通过");
        }

        if (test1Pass && test2Pass) {
            System.out.println("\n🎉 所有测试通过！空标题移除功能工作正常。");
        } else {
            System.out.println("\n❌ 部分测试失败，请检查实现。");
        }
    }
}
